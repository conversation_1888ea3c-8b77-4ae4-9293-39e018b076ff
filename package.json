{"name": "client-portfolio-frontend", "private": true, "version": "0.0.0", "scripts": {"start": "node server.cjs", "dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@anthropic-ai/sdk": "^0.5.0", "@radix-ui/react-icons": "^1.3.2", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "lucide-react": "^0.263.1", "multer": "^2.0.1", "papaparse": "^5.5.3", "pg": "^8.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-router-dom": "^6.23.0", "recharts": "^2.8.0", "uuid": "^9.0.0", "zustand": "^4.4.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "nodemon": "^3.0.1", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}, "type": "module"}